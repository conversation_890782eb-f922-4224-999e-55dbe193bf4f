/*
 * WiFi AP Mode Configuration for ESP32 File Server with OTA
 */

#include <string.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/event_groups.h"
#include "esp_system.h"
#include "esp_wifi.h"
#include "esp_event.h"
#include "esp_log.h"
#include "esp_netif.h"
#include "esp_mac.h"

#include "lwip/err.h"
#include "lwip/sys.h"

/* WiFi AP configuration */
#define WIFI_AP_SSID "ESP32-FileServer"
#define WIFI_AP_PASS "12345678"
#define WIFI_AP_CHANNEL 1
#define WIFI_AP_MAX_CONN 4

static const char *TAG = "wifi_ap";

static void wifi_event_handler(void *arg, esp_event_base_t event_base,
                               int32_t event_id, void *event_data)
{
    if (event_id == WIFI_EVENT_AP_STACONNECTED)
    {
        wifi_event_ap_staconnected_t *event = (wifi_event_ap_staconnected_t *)event_data;
        ESP_LOGI(TAG, "Station %02x:%02x:%02x:%02x:%02x:%02x joined, AID=%d",
                 event->mac[0], event->mac[1], event->mac[2],
                 event->mac[3], event->mac[4], event->mac[5], event->aid);
    }
    else if (event_id == WIFI_EVENT_AP_STADISCONNECTED)
    {
        wifi_event_ap_stadisconnected_t *event = (wifi_event_ap_stadisconnected_t *)event_data;
        ESP_LOGI(TAG, "Station %02x:%02x:%02x:%02x:%02x:%02x left, AID=%d",
                 event->mac[0], event->mac[1], event->mac[2],
                 event->mac[3], event->mac[4], event->mac[5], event->aid);
    }
}

esp_err_t wifi_init_ap(void)
{
    esp_err_t ret;

    ESP_LOGI(TAG, "开始初始化WiFi AP模式...");

    // 1. 初始化网络接口
    ret = esp_netif_init();
    if (ret != ESP_OK)
    {
        ESP_LOGE(TAG, "初始化网络接口失败: %s", esp_err_to_name(ret));
        return ret;
    }
    ESP_LOGI(TAG, "网络接口初始化成功");

    // 2. 创建默认事件循环
    ret = esp_event_loop_create_default();
    if (ret != ESP_OK && ret != ESP_ERR_INVALID_STATE)
    {
        ESP_LOGE(TAG, "创建事件循环失败: %s", esp_err_to_name(ret));
        return ret;
    }
    ESP_LOGI(TAG, "事件循环创建成功");

    // 3. 创建默认WiFi AP网络接口
    esp_netif_t *wifi_netif = esp_netif_create_default_wifi_ap();
    if (wifi_netif == NULL)
    {
        ESP_LOGE(TAG, "创建WiFi AP网络接口失败");
        return ESP_FAIL;
    }
    ESP_LOGI(TAG, "WiFi AP网络接口创建成功");

    // 4. 初始化WiFi配置（使用保守的配置）
    wifi_init_config_t cfg = WIFI_INIT_CONFIG_DEFAULT();
    // 减少缓冲区数量以降低内存压力
    cfg.static_rx_buf_num = 8;   // 默认10，减少到8
    cfg.dynamic_rx_buf_num = 16; // 默认32，减少到16
    cfg.dynamic_tx_buf_num = 16; // 默认32，减少到16
    cfg.rx_mgmt_buf_num = 3;     // 默认5，减少到3

    ret = esp_wifi_init(&cfg);
    if (ret != ESP_OK)
    {
        ESP_LOGE(TAG, "WiFi初始化失败: %s", esp_err_to_name(ret));
        return ret;
    }
    ESP_LOGI(TAG, "WiFi驱动初始化成功");

    // 等待WiFi驱动稳定
    vTaskDelay(pdMS_TO_TICKS(500));
    ESP_LOGI(TAG, "开始注册WiFi事件处理器...");

    // 5. 注册WiFi事件处理器
    ret = esp_event_handler_instance_register(WIFI_EVENT,
                                              ESP_EVENT_ANY_ID,
                                              &wifi_event_handler,
                                              NULL,
                                              NULL);
    if (ret != ESP_OK)
    {
        ESP_LOGE(TAG, "注册WiFi事件处理器失败: %s", esp_err_to_name(ret));
        esp_wifi_deinit();
        return ret;
    }
    ESP_LOGI(TAG, "WiFi事件处理器注册成功");

    // 等待事件处理器稳定
    vTaskDelay(pdMS_TO_TICKS(200));
    ESP_LOGI(TAG, "开始配置WiFi AP参数...");

    // 6. 配置WiFi AP参数
    wifi_config_t wifi_config = {
        .ap = {
            .ssid = WIFI_AP_SSID,
            .ssid_len = strlen(WIFI_AP_SSID),
            .channel = WIFI_AP_CHANNEL,
            .password = WIFI_AP_PASS,
            .max_connection = WIFI_AP_MAX_CONN,
            .authmode = WIFI_AUTH_WPA2_PSK,
            .pmf_cfg = {
                .required = false,
            },
        },
    };

    if (strlen(WIFI_AP_PASS) == 0)
    {
        wifi_config.ap.authmode = WIFI_AUTH_OPEN;
    }
    ESP_LOGI(TAG, "WiFi配置参数准备完成");

    // 7. 设置WiFi模式
    ESP_LOGI(TAG, "设置WiFi模式为AP...");
    ret = esp_wifi_set_mode(WIFI_MODE_AP);
    if (ret != ESP_OK)
    {
        ESP_LOGE(TAG, "设置WiFi模式失败: %s", esp_err_to_name(ret));
        esp_wifi_deinit();
        return ret;
    }
    ESP_LOGI(TAG, "WiFi模式设置为AP成功");

    // 等待模式设置稳定
    vTaskDelay(pdMS_TO_TICKS(300));

    // 8. 设置WiFi配置
    ESP_LOGI(TAG, "应用WiFi配置...");
    ret = esp_wifi_set_config(WIFI_IF_AP, &wifi_config);
    if (ret != ESP_OK)
    {
        ESP_LOGE(TAG, "设置WiFi配置失败: %s", esp_err_to_name(ret));
        esp_wifi_deinit();
        return ret;
    }
    ESP_LOGI(TAG, "WiFi配置设置成功");

    // 等待配置应用
    vTaskDelay(pdMS_TO_TICKS(500));

    // 9. 启动WiFi
    ESP_LOGI(TAG, "启动WiFi服务...");
    ret = esp_wifi_start();
    if (ret != ESP_OK)
    {
        ESP_LOGE(TAG, "启动WiFi失败: %s", esp_err_to_name(ret));
        esp_wifi_deinit();
        return ret;
    }
    ESP_LOGI(TAG, "WiFi启动命令执行成功");

    // 等待WiFi完全启动
    vTaskDelay(pdMS_TO_TICKS(1000));

    ESP_LOGI(TAG, "WiFi AP启动成功! SSID:%s password:%s channel:%d",
             WIFI_AP_SSID, WIFI_AP_PASS, WIFI_AP_CHANNEL);

    return ESP_OK;
}
