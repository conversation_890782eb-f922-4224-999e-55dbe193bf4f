/*
 * ESP32 BLE Mill Monitor Master - Main Application Entry Point
 *
 * This file contains the main application initialization and coordination
 * of all system components including BLE, WiFi, file system, and HTTP server.
 */

#include <stdio.h>
#include <string.h>
#include "esp_log.h"
#include "esp_err.h"
#include "esp_system.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"

// Component headers
#include "ble_master.h"
#include "ble_uart.h"
#include "wifi_ap.h"
#include "fs_mount.h"
#include "file_server.h"

static const char *TAG = "MAIN";

// File system mount point
#define MOUNT_POINT "/spiffs"

void app_main(void)
{
    esp_err_t ret;

    ESP_LOGI(TAG, "=== ESP32 BLE Mill Monitor Master 启动 ===");
    ESP_LOGI(TAG, "芯片型号: %s", CONFIG_IDF_TARGET);
    ESP_LOGI(TAG, "IDF版本: %s", esp_get_idf_version());

    // NVS（Non-Volatile Storage） 是一种非易失性存储系统，主要用于在设备掉电或重启后保留数据
    // 通常是以 key-value（键值对） 的形式存储数据, 保存配置参数（如 WiFi 名称、密码）
    ESP_LOGI(TAG, "初始化NVS存储...");
    ret = nvs_flash_init();
    if (ret == ESP_ERR_NVS_NO_FREE_PAGES || ret == ESP_ERR_NVS_NEW_VERSION_FOUND)
    {
        ESP_LOGW(TAG, "NVS需要擦除，正在重新初始化...");
        ret = nvs_flash_erase();
        if (ret != ESP_OK)
        {
            ESP_LOGE(TAG, "NVS擦除失败: %s", esp_err_to_name(ret));
            return;
        }
        ret = nvs_flash_init();
    }
    if (ret != ESP_OK)
    {
        ESP_LOGE(TAG, "NVS初始化失败: %s", esp_err_to_name(ret));
        return;
    }
    ESP_LOGI(TAG, "NVS存储初始化成功");

    // 等待系统稳定
    ESP_LOGI(TAG, "等待系统稳定...");
    vTaskDelay(pdMS_TO_TICKS(1000));

    // 初始化WiFi AP
    ESP_LOGI(TAG, "开始初始化WiFi AP...");
    ret = wifi_init_ap();
    if (ret != ESP_OK)
    {
        ESP_LOGE(TAG, "初始化WiFi失败: %s", esp_err_to_name(ret));
        ESP_LOGE(TAG, "系统将在5秒后重启...");
        vTaskDelay(pdMS_TO_TICKS(5000));
        esp_restart();
        return;
    }
    ESP_LOGI(TAG, "WiFi AP初始化成功");

    // 等待WiFi稳定
    vTaskDelay(pdMS_TO_TICKS(2000));
    /*
        // 挂载文件系统
        const char *base_path = "/data";
        ret = example_mount_storage(base_path);
        if (ret != ESP_OK)
        {
            ESP_LOGE(TAG, "挂载失败: %s", esp_err_to_name(ret));
            return;
        }

          // 启动文件服务器
          ret = example_start_file_server(base_path);
          if (ret != ESP_OK)
          {
              ESP_LOGE(TAG, "初始化文件服务器失败: %s", esp_err_to_name(ret));
              return;
          }

          // 初始化BLE Master功能（包含UART初始化）
          ret = ble_master_init();
          if (ret != ESP_OK)
          {
              ESP_LOGE(TAG, "初始化 BLE Master: %s, 系统在5秒后重启!", esp_err_to_name(ret));
              vTaskDelay(pdMS_TO_TICKS(5000));
              esp_restart();
              return;
          }

          // 等待系统稳定后启动BLE协议栈
          vTaskDelay(pdMS_TO_TICKS(1000));

          // 启动BLE协议栈
          ret = ble_master_start();
          if (ret != ESP_OK)
          {
              ESP_LOGE(TAG, "启动BLE协议栈失败:  %s", esp_err_to_name(ret));
              return;
          }
          */
    ESP_LOGI(TAG, "=== 系统初始化完成，进入主循环 ===");

    // 主循环 - 系统监控
    uint32_t loop_count = 0;
    while (1)
    {
        loop_count++;

        // 每10秒打印一次系统状态
        if (loop_count % 10 == 0)
        {
            ESP_LOGI(TAG, "系统运行正常 - 循环计数: %lu", loop_count);
            ESP_LOGI(TAG, "空闲内存: %lu bytes", esp_get_free_heap_size());
            ESP_LOGI(TAG, "最小空闲内存: %lu bytes", esp_get_minimum_free_heap_size());
        }

        vTaskDelay(pdMS_TO_TICKS(1000)); // 每1秒检查一次
    }
}
